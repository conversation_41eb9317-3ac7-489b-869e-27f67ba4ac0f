# ZimHealth-ID - Digital Health Records Management System

## 📋 Project Overview

ZimHealth-ID is a comprehensive digital health records management system designed for healthcare facilities in Zimbabwe. The system provides secure patient management, medical records tracking, prescription management, and appointment scheduling with QR code integration for efficient patient identification.

## 🏗️ System Architecture

### Technology Stack
- **Backend**: Django 4.2+ (Python web framework)
- **Database**: SQLite (development) / PostgreSQL (production ready)
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Tailwind CSS with custom government-grade design system
- **QR Code**: jsQR library for scanning, qrcode library for generation
- **Authentication**: Django's built-in authentication system

### Project Structure
```
ZimHealth-ID/
├── zimhealth_id/                 # Main Django project
│   ├── api/                      # Core application
│   │   ├── models.py            # Database models
│   │   ├── views.py             # Business logic
│   │   ├── forms.py             # Form handling
│   │   ├── urls.py              # URL routing
│   │   └── management/          # Custom management commands
│   ├── zimhealth_id/            # Project settings
│   │   ├── settings.py          # Configuration
│   │   ├── urls.py              # Main URL routing
│   │   └── wsgi.py              # WSGI configuration
│   ├── templates/               # HTML templates
│   ├── static/                  # CSS, JS, images
│   ├── media/                   # User uploads (QR codes, avatars)
│   └── manage.py                # Django management script
├── .zhid_venv/                  # Virtual environment
├── requirements.txt             # Python dependencies
└── README.md                    # Basic project info
```

## 🔧 Core Features

### 1. Patient Management
- **Patient Registration**: Complete patient profiles with demographics
- **QR Code Generation**: Automatic QR code creation for each patient
- **Patient Search**: Advanced search and filtering capabilities
- **Data Validation**: Comprehensive form validation and error handling

### 2. Medical Records
- **Record Creation**: Detailed medical record entry with patient linking
- **QR Code Scanning**: Scan patient QR codes to auto-populate patient data
- **Medical History**: Complete patient medical history tracking
- **Diagnosis Management**: Structured diagnosis entry and tracking

### 3. Prescription Management
- **Prescription Creation**: Link prescriptions to medical records
- **Medication Tracking**: Comprehensive medication database
- **Dosage Management**: Standardized frequency and dosage options
- **Prescription History**: Complete prescription tracking per patient

### 4. Appointment System
- **Appointment Scheduling**: Book and manage patient appointments
- **Calendar Integration**: Visual appointment calendar
- **Status Tracking**: Track appointment status (scheduled, completed, cancelled)
- **Patient Linking**: Direct patient-appointment relationships

### 5. QR Code Integration
- **Patient QR Codes**: Each patient gets a unique QR code
- **Mobile Scanning**: Camera-based QR code scanning in web browser
- **Auto-Population**: Scan QR codes to automatically select patients
- **Cross-Platform**: Works on desktop and mobile devices

## 🎨 Design System

### Government-Grade Styling
- **Professional Theme**: Clean, government-appropriate design language
- **Consistent Colors**: Blue-based color scheme with medical accents
- **Typography**: Clear, readable fonts with proper hierarchy
- **Responsive Design**: Mobile-first approach with desktop optimization

### UI Components
- **Buttons**: `government-filter-button`, `add-patient-button` classes
- **Modals**: Consistent modal design across all features
- **Forms**: Standardized form layouts with validation feedback
- **Navigation**: Intuitive navigation with breadcrumbs and clear paths

## 🗄️ Database Schema

### Core Models
1. **Patient**: Demographics, contact info, medical details, QR code
2. **MedicalRecord**: Medical history, diagnosis, symptoms, treatment
3. **Prescription**: Medication details, dosage, frequency, duration
4. **Appointment**: Scheduling, status, patient linking, notes

### Key Relationships
- Patient → MedicalRecord (One-to-Many)
- MedicalRecord → Prescription (One-to-Many)
- Patient → Appointment (One-to-Many)
- All models include audit fields (created_at, updated_at)

## 🚀 Deployment Guide

### Prerequisites
- Python 3.8+
- Virtual environment support
- Web server (Apache/Nginx)
- Database server (PostgreSQL recommended for production)

### Installation Steps
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd ZimHealth-ID
   ```

2. **Setup Virtual Environment**
   ```bash
   python -m venv .zhid_venv
   source .zhid_venv/bin/activate  # Linux/Mac
   # or
   .zhid_venv\Scripts\activate     # Windows
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Database Setup**
   ```bash
   cd zimhealth_id
   python manage.py migrate
   python manage.py create_sample_data --patients 5
   ```

5. **Create Superuser**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run Development Server**
   ```bash
   python manage.py runserver
   ```

### Production Configuration
- Update `settings.py` for production environment
- Configure PostgreSQL database
- Set up static file serving
- Configure HTTPS for QR code scanning
- Set proper environment variables

## 🔒 Security Features

### Data Protection
- CSRF protection on all forms
- SQL injection prevention through Django ORM
- XSS protection with template escaping
- Secure file upload handling

### Authentication
- Django's built-in authentication system
- Session-based user management
- Password validation and hashing
- User permission management

## 📱 QR Code System

### QR Code Format
```
ZimHealth-ID: ZH-YYYY-XXXXXX
Name: Patient Full Name
DOB: YYYY-MM-DD
Blood Type: A+/B+/AB+/O+/A-/B-/AB-/O-
Emergency: +263XXXXXXXXX
```

### Scanning Technology
- **Library**: jsQR for client-side scanning
- **Camera Access**: WebRTC getUserMedia API
- **Fallback**: Manual patient ID entry
- **Cross-Browser**: Compatible with modern browsers

## 🛠️ Management Commands

### Data Management
- `create_sample_data`: Generate sample patients and records
- `clear_seed_data`: Clean database of test data
- `ensure_data_consistency`: Fix data integrity issues

### Usage Examples
```bash
# Create 5 sample patients with complete data
python manage.py create_sample_data --patients 5

# Clean all test data
python manage.py clear_seed_data --confirm

# Fix data consistency issues
python manage.py ensure_data_consistency --fix --verbose
```

## 🔧 Customization

### Adding New Features
1. Create models in `api/models.py`
2. Add views in `api/views.py`
3. Create templates in `templates/api/`
4. Update URL routing in `api/urls.py`
5. Add styling following design system

### Styling Guidelines
- Use existing CSS classes for consistency
- Follow Tailwind CSS conventions
- Maintain government-grade professional appearance
- Ensure mobile responsiveness

## 📊 Sample Data

The system includes 5 sample patients with complete data:
- Lisa Rodriguez (ZH-2025-261792)
- David Miller (ZH-2025-155103)
- Patricia Martinez (ZH-2025-991989)
- James Williams (ZH-2025-067681)
- Mary Miller (ZH-2025-014024)

Each patient includes:
- Complete demographic information
- Medical records with diagnoses
- Prescription history
- Appointment records
- Generated QR codes

## 🐛 Troubleshooting

### Common Issues
1. **QR Scanner Not Working**: Ensure HTTPS and camera permissions
2. **Database Errors**: Run migrations and check data consistency
3. **Static Files**: Collect static files for production
4. **Permission Errors**: Check file permissions for media directory

### Debug Mode
- Set `DEBUG = True` in settings.py for development
- Check Django logs for detailed error information
- Use browser developer tools for frontend issues

## 📞 Support

For technical support or feature requests, refer to the project repository or contact the development team.

---

**Version**: 1.0.0  
**Last Updated**: August 2025  
**License**: Proprietary  
**Developed for**: Zimbabwe Healthcare System
